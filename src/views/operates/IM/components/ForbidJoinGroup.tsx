import React, { useEffect, useMemo, useState } from 'react';
import { Drawer, Form, Select, Button, Modal, message } from 'antd';
import { debounce } from 'lodash';
import { communityApi, opApi } from '@app/api';
import useTable from '@utils/useTable';
import NewTable from '@utils/newTable';

const { confirm } = Modal;

interface ForbidJoinGroupProps {
  visible: boolean;
  onCancel: () => void;
  form: any;
  circleId?: string;
}

// 与账号搜索返回结构对齐（参考 CreateGroupChat.tsx）
export interface UserItem {
  id: string;
  nick_name: string;
  chao_id: string;
}

const ForbidJoinGroup: React.FC<ForbidJoinGroupProps> = ({ visible, onCancel, form, circleId }) => {
  const { getFieldDecorator, resetFields } = form;

  // 使用独立的表格状态管理
  const { tableList, loading: tableLoading, getTableList } = useTable({
    filter: () => circleId ? { circle_id: circleId } : {},
    api: opApi.getGroupChatBlackList,
    index: 'list'
  });

  // 搜索 UI 状态（完全复用 CreateGroupChat.tsx 的交互）
  const [selectedAccount, setSelectedAccount] = useState<string | undefined>();
  const [accountSuggestions, setAccountSuggestions] = useState<UserItem[]>([]);

  // 加载状态
  const [loading, setLoading] = useState(false);

  // 获取禁止加群列表数据
  const getData = () => {
    if (circleId) {
      getTableList(1); // 使用useTable的getTableList方法
    }
  };

  // 抽屉开启时加载初始数据
  useEffect(() => {
    if (visible && circleId) {
      getData();
    }
  }, [visible, circleId]);

  // 关闭抽屉时复位
  const handleClose = () => {
    resetFields();
    setSelectedAccount(undefined);
    setAccountSuggestions([]);
    onCancel();
  };

  // 搜索（平移 CreateGroupChat.tsx 的 API 与节流逻辑）
  const handleAccountSearch = useMemo(
    () =>
      debounce((value: string) => {
        if (value) {
          communityApi
            .recommendAccount_Search({ keyword: value })
            .then((res: any) => {
              setAccountSuggestions(res?.data?.list || []);
            })
            .catch(() => setAccountSuggestions([]));
        } else {
          setAccountSuggestions([]);
        }
      }, 300),
    []
  );

  const handleAccountChange = (value?: string) => {
    setSelectedAccount(value);
    // 如果清空选择，也清空下拉数据
    if (!value) {
      setAccountSuggestions([]);
    }
  };

  // 选择某个账号时，弹出二次确认
  const handleAccountSelect = (value: string) => {
    const item = accountSuggestions.find((s) => `${s.id}` === `${value}`);
    if (!item) return;

    // 弹出二次确认弹窗
    confirm({
      title: '是否禁止该用户加入圈子群聊',
      content: `用户：${item.nick_name}（小潮号：${item.chao_id}）`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        // 调用添加接口
        if (!circleId) {
          message.error('请先选择圈子');
          return Promise.reject();
        }

        setLoading(true);
        return opApi.createGroupChatBlackList({
          account_id: item.id,
          circle_id: circleId
        }).then(() => {
          message.success('添加成功');
          // 刷新列表
          getData();
          // 清空选择
          clearSelection();
        }).catch(() => {
          message.error('添加失败');
          return Promise.reject();
        }).finally(() => {
          setLoading(false);
        });
      },
      onCancel: () => {
        // 直接清空选择
        clearSelection();
      }
    });
  };

  // 清空选择的数据
  const clearSelection = () => {
    setSelectedAccount(undefined);
    setAccountSuggestions([]);
    form.setFieldsValue({
      accountSearch: undefined
    });
  };

  // 移除用户
  const removeUser = (record: UserItem) => {
    confirm({
      title: '确定移除该用户?',
      content: `用户：${record.nick_name}（小潮号：${record.chao_id}）`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        if (!circleId) {
          message.error('请先选择圈子');
          return Promise.reject();
        }

        setLoading(true);
        return opApi.deleteGroupChatBlackList({
          id: record.id,
          circle_id: circleId
        }).then(() => {
          message.success('移除成功');
          // 刷新列表
          getData();
        }).catch(() => {
          message.error('移除失败');
          return Promise.reject();
        }).finally(() => {
          setLoading(false);
        });
      },
    });
  };



  // 表格列
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 80,
      render: (_: any, __: UserItem, index: number) => {
        const { current = 1, size = 10 } = tableList || {};
        return (current - 1) * size + index + 1;
      },
    },
    {
      title: '账号昵称',
      dataIndex: 'nick_name',
    },
    {
      title: '小潮号',
      dataIndex: 'chao_id',
      width: 160,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: UserItem) => (
        <a onClick={() => removeUser(record)}>移除</a>
      ),
    },
  ];

  return (
    <Drawer
      title="禁止加入圈子群聊"
      visible={visible}
      onClose={handleClose}
      width={800}
      maskClosable={false}
      destroyOnClose
    >
      {/* 说明文案 */}
      <div style={{ padding: '0 8px 16px', color: '#666' }}>
        对应账号将被移出圈子群聊，且无法再加入圈子任一群聊
      </div>

      {/* 搜索区域（保持与 CreateGroupChat.tsx 一致风格） */}
      <Form labelCol={{ span: 4 }} wrapperCol={{ span: 16 }}>
        <Form.Item label="账号" required={false}>
          {getFieldDecorator('accountSearch')(
            <Select
              value={selectedAccount}
              showSearch
              allowClear
              placeholder="输入昵称或小潮号查找账号"
              filterOption={false}
              onChange={handleAccountChange}
              onSearch={handleAccountSearch as any}
              onSelect={handleAccountSelect}
            >
              {accountSuggestions.map((d: any) => (
                <Select.Option
                  key={d.id}
                  value={d.id}
                  style={{ whiteSpace: 'pre-wrap' }}
                >
                  {`${['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] || ''}${d.nick_name} | 小潮号：${d.chao_id}`}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
      </Form>

      {/* 列表容器 */}
      <div style={{
        height: 'calc(100vh - 280px)',
        minHeight: '500px',
      }}>
        <NewTable
          rowKey="id"
          columns={columns}
          pagination={true}
          tableList={tableList}
          getTableList={getTableList}
          loading={tableLoading || loading}
          tableProps={{
            scroll: { y: 'calc(100vh - 380px)' },
            size: 'middle'
          }}
        />
      </div>

      {/* 底部操作栏 */}
      <div
        style={{
          position: 'absolute',
          bottom: 0,
          right: 0,
          left: 0,
          borderTop: '1px solid #e8e8e8',
          padding: '12px 24px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1000,
        }}
      >
        <Button onClick={handleClose} style={{ marginRight: 8 }}>
          关闭
        </Button>
      </div>
    </Drawer>
  );
};

export default Form.create<ForbidJoinGroupProps>({ name: 'forbidJoinGroup' })(ForbidJoinGroup);

