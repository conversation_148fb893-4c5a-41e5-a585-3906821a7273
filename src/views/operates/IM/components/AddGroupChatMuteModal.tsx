import React, { useState, useEffect } from 'react';
import { Form, Select, message, Modal, Button, Input } from 'antd';
import { debounce } from 'lodash';
import { communityApi } from '@app/api';
import { opApi } from '@app/api/opApi';
import { iMReportTypeMap } from '@app/utils/utils';

const { Option } = Select;

interface AddGroupChatMuteModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  form: any;
  existingMuteUsers?: string[]; // 已禁言用户ID列表，用于重复检查
}

interface FormData {
  account_id: string;
  block_days: string;
  reason_type: string;
  chat_type: number;
  remark?: string;
}

interface UserOption {
  id: string;
  nick_name: string;
  chao_id: string;
  cert_type: number;
}

const AddGroupChatMuteModal: React.FC<AddGroupChatMuteModalProps> = ({ 
  visible, 
  onCancel, 
  onOk, 
  form,
  existingMuteUsers = []
}) => {
  const { getFieldDecorator, validateFields, resetFields, getFieldValue } = form;

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [userSuggestions, setUserSuggestions] = useState<UserOption[]>([]);

  // ✅ 监听弹窗状态变化，确保关闭时数据初始化
  useEffect(() => {
    if (!visible) {
      // 弹窗关闭时重置所有状态
      setLoading(false);
      setUserSuggestions([]);
      // 延迟重置表单，避免动画期间的闪烁
      setTimeout(() => {
        resetFields();
      }, 100);
    }
  }, [visible, resetFields]);

  // 表单布局
  const formLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  // 禁言时长选项（与私信黑名单保持一致）
  const blockDaysOptions = [
    { value: '1', label: '1天' },
    { value: '3', label: '3天' },
    { value: '7', label: '7天' },
    { value: '30', label: '30天' },
    { value: '-1', label: '永久' },
  ];

  // 处理表单提交
  const handleSubmit = () => {
    validateFields((err: any, values: FormData) => {
      if (!err) {
        // 检查是否已在禁言状态
        if (existingMuteUsers.includes(values.account_id)) {
          message.error('该账号已禁言');
          return;
        }

        setLoading(true);

        // 处理数据
        const submitData = {
          ...values,
          chat_type: 1, // 群聊类型
        };

        // ✅ 使用真实API接口 - 添加黑名单接口
        console.log('添加群聊黑名单账号', submitData);

        opApi.addIMBlackUser(submitData).then(() => {
          setLoading(false);
          message.success('添加成功');
          onOk(submitData);
          // ✅ 成功后重置数据并关闭弹窗
          handleCancel();
        }).catch(() => {
          setLoading(false);
          // 不显示错误提示，按要求处理
          // ✅ 失败后也重置数据并关闭弹窗
          handleCancel();
        });
      }
    });
  };

  // 处理取消 - 完整的数据初始化
  const handleCancel = () => {
    // ✅ 重置表单字段
    resetFields();

    // ✅ 清空用户搜索建议
    setUserSuggestions([]);

    // ✅ 重置加载状态
    setLoading(false);

    // ✅ 调用父组件的取消回调
    onCancel();
  };

  // 处理用户搜索建议（复用CreateGroupChat的账号选择功能）
  const handleUserSearch = debounce((value: string) => {
    if (value) {
      // 调用接口获取用户建议列表
      communityApi
        .recommendAccount_Search({ keyword: value })
        .then((res: any) => {
          setUserSuggestions(res.data?.list || []);
        })
        .catch(() => {
          setUserSuggestions([]);
        });
    } else {
      setUserSuggestions([]);
    }
  }, 300);

  // 自定义账号验证器
  const validateAccount = (rule: any, value: any, callback: any) => {
    if (!value) {
      callback('请输入昵称或小潮号');
      return;
    }
    callback();
  };

  // 自定义禁言时长验证器
  const validateMuteDuration = (rule: any, value: any, callback: any) => {
    if (!value) {
      callback('请选择禁言时长');
      return;
    }
    callback();
  };

  // 自定义禁言理由验证器
  const validateReason = (rule: any, value: any, callback: any) => {
    if (!value) {
      callback('请选择禁言理由');
      return;
    }
    callback();
  };

  return (
    <Modal
      title="添加群聊禁言账号"
      visible={visible}
      onCancel={handleCancel}
      width={500}
      maskClosable={false}
      destroyOnClose={true}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit} loading={loading}>
          确定
        </Button>,
      ]}
    >
      <Form {...formLayout}>
        {/* 禁言账号 */}
        <Form.Item label="禁言账号" required>
          {getFieldDecorator('account_id', {
            rules: [
              {
                required: true,
                validator: validateAccount,
              },
            ],
          })(
            <Select
              placeholder="输入昵称或小潮号查找"
              onSearch={handleUserSearch}
              showSearch
              allowClear={true}
              filterOption={false}
            >
              {userSuggestions.map((user: UserOption) => (
                <Select.Option
                  key={user.id}
                  value={user.id}
                  style={{
                    whiteSpace: 'pre-wrap',
                  }}
                >
                  {`${['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][user.cert_type] || ''}${
                    user.nick_name
                  } | 小潮号：${user.chao_id}`}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>

        {/* 禁言时长 */}
        <Form.Item label="禁言时长" required>
          {getFieldDecorator('block_days', {
            initialValue: '1',
            rules: [
              {
                required: true,
                validator: validateMuteDuration,
              },
            ],
          })(
            <Select placeholder="请选择禁言时长">
              {blockDaysOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          )}
        </Form.Item>

        {/* 禁言理由 */}
        <Form.Item label="禁言理由" required>
          {getFieldDecorator('reason_type', {
            initialValue: '1',
            rules: [
              {
                required: true,
                validator: validateReason,
              },
            ],
          })(
            <Select placeholder="请选择禁言理由">
              {Object.keys(iMReportTypeMap).map((key) => (
                <Option key={key} value={key}>
                  {iMReportTypeMap[key]}
                </Option>
              ))}
            </Select>
          )}
        </Form.Item>

        {/* 备注 */}
        <Form.Item label="备注">
          {getFieldDecorator('remark', {
            initialValue: '',
          })(
            <Input.TextArea
              placeholder="添加备注信息，仅后台可见"
              rows={4}
              maxLength={200}
            />
          )}
        </Form.Item>

      </Form>

      {/* 底部说明文字 */}
      <div style={{
        color: '#666',
        fontSize: '14px',
        marginTop: '16px',
        textAlign: 'center'
      }}>
        禁言账号加入的所有群聊，在禁言期均不可发言。
      </div>
    </Modal>
  );
};

export default Form.create<AddGroupChatMuteModalProps>({ name: 'addGroupChatMuteModal' })(AddGroupChatMuteModal);
