import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A } from '@components/common';
import {
  getCrumb,
  iMReportTypeMap,
  objectToQueryString,
  searchToObject,
  setMenuHook,
  UserDetail,
} from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Button,
  Select,
  Icon,
  Modal,
  message,
  Radio,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, communityApi, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA } from '@components/permItems';
import moment from 'moment';
import '@components/business/styles/business.scss';
import _ from 'lodash';
import HandleIMViolationModal from './components/handleIMViolationModal';
export default function ImReportListManager(props: any) {
  const [accountOptions, setAccountOptions] = useState([]);

  const [filter, setFilter] = useState<any>({
    status: parseInt(searchToObject().status ?? 0),
    reason_type: '',
    messageType: '', // ✅ 新增消息类型筛选字段
  });

  const [search, setSearch] = useState<any>({
    search_type: 1,
    keyword: '',
    user_id: '',
  });

  const dispatch = useDispatch();
  const history = useHistory();
  const {
    current,
    size,
  } = useSelector((state: any) => state.tableList);
  const { run } = useXHR();

  const [userDetail, setUserDetail] = useState({
    key: null as number | null,
    visible: false,
    detail: null,
  });

  const [handleIMViolationModal, setHandleIMViolationModal] = useState({
    visible: false,
    record: null,
    key: '' as string | number,
  });

  const getList = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;

    // ✅ 将前端 messageType 字段映射为后端期望的 chat_type 字段
    const apiFilter = { ...newFilter };
    if (newFilter.messageType !== undefined && newFilter.messageType !== '') {
      apiFilter.chat_type = newFilter.messageType;
      delete apiFilter.messageType;
    }

    dispatch(getTableList('getIMComplainList', 'list', { current: cur, size, ...apiFilter }));
  };



  // 显示用户详情
  const showUserDetailModal = (id: any, visible: boolean) => {
    run(userApi.getUserDetail, { accountId: id }, true).then((r: any) => {
      setUserDetail({
        key: Date.now(),
        visible,
        detail: r.data.account,
      });
    });
  };

  const handleViolation = (record: any) => {
    setHandleIMViolationModal({
      visible: true,
      record,
      key: Date.now(),
    });
  };

  const handleNoViolation = (record: any) => {
    Modal.confirm({
      title: '消息未违规',
      content:
        filter.status == 0
          ? '*标记未违规后，举报人将会收到系统通知'
          : '如果被举报人已在消息黑名单、且需要移除，请前往相应的管理页面手动操作',
      onOk: () => {
        run(api.updateIMComplainStatus, { id: record.id, status: 1 }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const getColumns = () => {
    let values = [
      {
        title: '举报人',
        dataIndex: 'from_account_name',
        width: 150,
        render: (text: any, record: any) => (
          <A onClick={() => showUserDetailModal(record.from_account_id, true)}>{text}</A>
        ),
      },
      {
        title: '被举报人',
        dataIndex: 'target_account_name',
        width: 150,
        render: (text: any, record: any) => (
          <A onClick={() => showUserDetailModal(record.target_account_id, true)}>{text}</A>
        ),
      },
      {
        title: '举报类型',
        dataIndex: 'reason_type',
        width: 150,
        render: (_: any, record: any) => {
          return iMReportTypeMap[`${record.reason_type}`];
        },
      },
      {
        title: '消息内容',
        dataIndex: 'msg_content',
      },
      {
        title: '消息类型',
        dataIndex: 'chat_type',
        width: 100,
        render: (_: any, record: any) => {
          // ✅ 修正字段映射：chat_type 0→私信, 1→群聊
          const chatType = record.chat_type;
          if (chatType === 0) return '私信';
          if (chatType === 1) return '群聊';
        },
      },
      {
        title: '来源群',
        dataIndex: 'group_name',
        width: 150,
        render: (text: any, record: any) => {
          // ✅ 修正逻辑：如果是私信（chat_type为0）或者没有群名称，显示"-"
          if (record.chat_type === 0 || !text) {
            return '';
          }
          return text;
        },
      },
      {
        title: '消息时间',
        dataIndex: 'msg_date',
        width: 90,
        render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '举报时间',
        dataIndex: 'created_at',
        width: 90,
        render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      },
      ...(filter.status != 0
        ? [
            {
              title: '操作时间',
              dataIndex: 'updated_at',
              width: 90,
              render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
            },
            {
              title: '操作人',
              dataIndex: 'updated_by',
              width: 90,
            },
          ]
        : []),
      ...(filter.status == 0
        ? [
            {
              title: '操作',
              key: 'op',
              render: (_: any, record: any) => (
                <span>
                  <PermA perm="im_complain:update" onClick={() => handleViolation(record)}>
                    违规
                  </PermA>
                  &nbsp;
                  <PermA perm="im_complain:update" onClick={() => handleNoViolation(record)}>
                    未违规
                  </PermA>
                </span>
              ),
              width: 140,
            },
          ]
        : []),
    ];
    return values;
  };

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, props);
    handleAccountSearch('');
  }, []);

  useEffect(() => {
    getList(true);
  }, [filter]);

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const value = search.search_type == 1 ? search.keyword : search.user_id;
      setFilter({
        ...filter,
        keyword: value || '',
        search_type: search.search_type,
      });
    }
  };

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setAccountOptions([]);
      return;
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    communityApi
      .recommendAccount_Search({ keyword: val })
      .then((res: any) => {
        setAccountOptions(res?.data?.list || []);
      })
      .catch(() => {});
  }, 500);

  const onChangeType = (v: any, name: any) => {
    let { pathname: path } = history.location;
    const obj = searchToObject();
    obj[name] = v;
    path = `${path}?${objectToQueryString(obj)}`;
    history.replace(path);
    setFilter({
      ...filter,
      [name]: v,
    });
  };

  const searchInputChange = (e: any) => {
    setSearch({
      ...search,
      search_type: e,
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
       
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Radio.Group
              defaultValue={filter.status}
              buttonStyle="solid"
              style={{ marginRight: 8 }}
              onChange={(e) => onChangeType(e.target.value, 'status')}
            >
              <Radio.Button value={0}>待处理</Radio.Button>
              <Radio.Button value={1}>未违规</Radio.Button>
              <Radio.Button value={2}>违规</Radio.Button>
            </Radio.Group>

            <Select
              value={filter.reason_type}
              style={{ width: 150, marginRight: 8, marginLeft: 8 }}
              onChange={(val) => {
                setFilter({
                  ...filter,
                  reason_type: val,
                });
              }}
            >
              <Select.Option value={''}>举报类型</Select.Option>
              {Object.keys(iMReportTypeMap).map((key) => {
                return <Select.Option key={key} value={key}>{iMReportTypeMap[key]}</Select.Option>;
              })}
            </Select>

            <Select
              value={filter.messageType}
              style={{ width: 120, marginRight: 8 }}
              onChange={(val) => {
                setFilter({
                  ...filter,
                  messageType: val,
                });
              }}
            >
              <Select.Option value={''}>消息类型</Select.Option>
              {/* ✅ 修正筛选选项：0→私信, 1→群聊 */}
              <Select.Option value={0}>私信</Select.Option>
              <Select.Option value={1}>群聊</Select.Option>
            </Select>
          </Col>

          <Col span={12} style={{ textAlign: 'right' }}>
            <Select
              value={search.search_type}
              style={{ width: 150, marginRight: 8, marginLeft: 8 }}
              onChange={searchInputChange}
            >
              <Select.Option value={1}>消息内容</Select.Option>
              <Select.Option value={2}>举报人</Select.Option>
              <Select.Option value={3}>被举报人</Select.Option>
            </Select>
            {search.search_type == 1 ? (
              <Input
                style={{ width: 160, marginRight: 8 }}
                onKeyDown={handleKey}
                value={search.keyword}
                placeholder="请输入搜索内容"
                onChange={(e) =>
                  setSearch({
                    ...search,
                    keyword: e.target.value,
                  })
                }
              />
            ) : (
              <Select
                style={{ width: 200, marginRight: 8 }}
                value={search.user_id}
                placeholder="输入用户昵称或小潮号"
                onSearch={handleAccountSearch}
                showSearch
                allowClear={true}
                filterOption={false}
                onChange={(val) => {
                  setSearch({
                    ...search,
                    user_id: val,
                  });
                }}
              >
                {accountOptions.map((d: any) => (
                  <Select.Option
                    style={{
                      whiteSpace: 'pre-wrap',
                    }}
                    key={d.id}
                    value={d.id}
                  >
                    {props.optionMap
                      ? props.optionMap(d)
                      : `${
                          ['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] + d.nick_name
                        } | 小潮号： ${d.chao_id}`}
                  </Select.Option>
                ))}
              </Select>
            )}
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" />
              搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getIMComplainList"
          index="list"
          filter={filter}
          columns={getColumns()}
          rowKey="id"
          pagination={true}
        />
      </div>

      <Modal
        visible={userDetail.visible}
        key={userDetail.key}
        title="用户详情"
        width={800}
        onCancel={() => setUserDetail({ ...userDetail, visible: false })}
        onOk={() => setUserDetail({ ...userDetail, visible: false })}
      >
        {userDetail.visible && <UserDetail detail={userDetail.detail} />}
      </Modal>

      <HandleIMViolationModal
        {...handleIMViolationModal}
        onCancel={() => setHandleIMViolationModal({ ...handleIMViolationModal, visible: false })}
        onOk={() => {
          setHandleIMViolationModal({ ...handleIMViolationModal, visible: false });
          getList();
        }}
      ></HandleIMViolationModal>
    </>
  );
}
