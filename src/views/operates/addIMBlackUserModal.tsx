import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Radio,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import _ from 'lodash';
import { communityApi, opApi } from '@app/api';
import { FileUploader } from '@app/components/common';
import { iMReportTypeMap } from '@app/utils/utils';

const AddIMBlackUserModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator, getFieldValue } = props.form;
  const [accountOptions, setAccountOptions] = useState([]);

  const column = [
    {
      title: '潮新闻ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '新闻频道',
      key: 'type',
      dataIndex: 'channel_name',
      width: 90,
    },
    {
      title: '新闻标题',
      key: 'list_title',
      dataIndex: 'list_title',
    },
  ];

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  useEffect(() => {
    if (props.visible) {
      handleAccountSearch('');
    }
  }, [props.visible]);

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setAccountOptions([]);
      return;
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    communityApi
      .recommendAccount_Search({ keyword: val })
      .then((res) => {
        setAccountOptions(res?.data?.list || []);
      })
      .catch(() => {});
  }, 500);

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const params: any = {
          ...values,
          chat_type: 0, // 私信类型
        };

        opApi
          .addIMBlackUser(params)
          .then((res: any) => {
            message.success('添加成功');
            setLoading(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      width={500}
      visible={props.visible}
      title="添加私信黑名单账号"
      key={``}
      onCancel={() => {
        props.onCancel && props.onCancel();
      }}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
    >
      <Spin spinning={false}>
        <Form {...formLayout} onSubmit={handleSubmit}>
          <Form.Item label="拉黑账号">
            {getFieldDecorator('account_id', {
              initialValue: undefined,
              rules: [{ required: true, message: '请选择用户' }],
            })(
              <Select
                // value={reporter}
                placeholder="输入昵称或小潮号查找"
                onSearch={handleAccountSearch}
                showSearch
                filterOption={false}
              >
                {accountOptions.map((d: any) => (
                  <Select.Option key={d.id} value={d.id}>
                    {props.optionMap
                      ? props.optionMap(d)
                      : `${
                          ['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] + d.nick_name
                        } | 小潮号： ${d.chao_id}`}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>

          <Form.Item label="拉黑时长">
            {getFieldDecorator('block_days', {
              initialValue: '1',
              rules: [{ required: true, message: '请选择添加方式' }],
            })(
              <Select>
                <Select.Option value="1">1天</Select.Option>
                <Select.Option value="3">3天</Select.Option>
                <Select.Option value="7">7天</Select.Option>
                <Select.Option value="30">30天</Select.Option>
                <Select.Option value="-1">永久</Select.Option>
              </Select>
            )}
          </Form.Item>

          <Form.Item label="拉黑理由">
            {getFieldDecorator('reason_type', {
              initialValue: '1',
              rules: [{ required: true, message: '请选择添加方式' }],
            })(
              <Select>
                {Object.keys(iMReportTypeMap).map((key, index) => {
                  return <Select.Option value={key}>{iMReportTypeMap[key]}</Select.Option>;
                })}
              </Select>
            )}
          </Form.Item>

          <Form.Item label="备注">
            {getFieldDecorator('remark', {
              initialValue: '',
            })(<Input.TextArea rows={4} placeholder="添加备注信息，仅后台可见" />)}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'AddIMBlackUserModal' })(
  forwardRef<any, any>(AddIMBlackUserModal)
);
