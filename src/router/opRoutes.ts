import { lazy } from 'react';

const PushNotifyList = lazy(() => import('@views/operates/pushNotify'));
const SensitiveWords = lazy(() => import('@views/operates/sensitiveWords'));
const StartPageDevices = lazy(() => import('@views/operates/startPageDevices'));
const StartPage = lazy(() => import('@views/operates/startPageMgr'));
const AboutManager = lazy(() => import('@views/operates/aboutManager'));
const PrivacyPolicy = lazy(() => import('@views/operates/privacyPolicy'));
const WhiteHost = lazy(() => import('@views/operates/whiteHost'));
const AppFeatureSwitch = lazy(() => import('@views/operates/appFeatureSwitch'));
const ProposalManager = lazy(() => import('@views/operates/proposalManager'));
const ReadRankMgr = lazy(() => import('@views/operates/readRankMgr'));
const TopicManager = lazy(() => import('@views/operates/topicManager'));
const TopicAudit = lazy(() => import('@views/operates/topicAudit'));
const UGCTopicRecommend = lazy(() => import('@views/operates/ugcTopicRecommend'));
const TopicArticleList = lazy(() => import('@app/views/operates/topicArticleList'));
const TopicCategoryMgr = lazy(() => import('@views/operates/topicCategoryMgr'));
const StickerManager = lazy(() => import('@app/views/operates/stickerManager'));
const RedPacket = lazy(() => import('@app/views/operates/redPacket'));
const MusicManager = lazy(() => import('@views/operates/musicManager'));
const MusicCategoryManager = lazy(() => import('@views/operates/musicCategoryManager'));
const RedpacketTotal = lazy(() => import('@views/operates/redPacketTotal'));
const RedpacketUserTotal = lazy(() => import('@views/operates/redPacketUserTotal'));
const RedpacketSingleTotal = lazy(() => import('@views/operates/redPacketSingleTotal'));
const HotNews = lazy(() => import('@views/operates/hotNews'));
const ActivityManager = lazy(() => import('@views/operates/activityManager'));
const AdamantineMgr = lazy(() => import('@views/operates/adamantineMgr'));
const HotWord = lazy(() => import('@views/operates/hotWord'));
const OrgContentRecommend = lazy(() => import('@views/operates/tmhContentRecommend'));
const MedalManager = lazy(() => import('@views/operates/medalManager'));
const RedpacketAgreement = lazy(() => import('@views/operates/redpacketagreement'));
const SignManager = lazy(() => import('@views/operates/sign'));
const ByteStickerManager = lazy(() => import('@views/operates/byteStickerManager'));
const ArSceneManager = lazy(() => import('@views/operates/arSceneManager'));
const MovieCouponsManager = lazy(() => import('@views/operates/movieCouponsManager'));
const CommentOperate = lazy(() => import('@views/operates/CommentOperate'));
const ColumnManager = lazy(() => import('@views/operates/ColumnManager'));
const ColumnGroupMgr = lazy(() => import('@views/operates/columnGroupMgr'));
const NewsColumnArticleList = lazy(() => import('@app/views/operates/NewsColumnArticleList'));
const AppAdvert = lazy(() => import('@app/views/operates/appAdvert'));
const CommercialAd = lazy(() => import('@app/views/operates/commercialAd'));
const CommercialAdCode = lazy(() => import('@app/views/operates/commercialAdCode'));
const TagManager = lazy(() => import('@app/views/operates/tagManager'));
const TagContentList = lazy(() => import('@app/views/operates/tagContentList'));
const AppThemeMgr = lazy(() => import('@views/operates/appThemeMgr'));
const HomeRemind = lazy(() => import('@views/operates/homeRemind'));
const TopManuscript = lazy(() => import('@app/views/operates/topManuscriptMgr'));
const longVideo = lazy(() => import('@app/views/operates/longVideo'));
const longVideoUser = lazy(() => import('@app/views/operates/longVideoUser'));
const RankingManager = lazy(() => import('@app/views/operates/rankingManager'));
const AssistantManager = lazy(() => import('@app/views/operates/assistantManager'));
const PartyNewsMgr = lazy(() => import('@app/views/operates/partyNewsMgr'));
const HdzxMgr = lazy(() => import('@app/views/operates/hdzxMgr'));
const AudioManager = lazy(() => import('@app/views/operates/audioManager'));
const ImManager = lazy(() => import('@app/views/operates/imManager'));
const ImWhiteListManager = lazy(() => import('@app/views/operates/imWhiteListManager'));
const ImBlackListManager = lazy(() => import('@app/views/operates/imBlackListManager'));
const ImReportListManager = lazy(() => import('@app/views/operates/imReportListManager'));
const AiFeedback = lazy(() => import('@app/views/operates/aiFeedback'));
const CreativeCenter = lazy(() => import('@app/views/operates/creativeCenter'));
const EditorChoiceness = lazy(() => import('@app/views/operates/editorChoiceness'));
const ColumnClassifyMgr = lazy(() => import('@app/views/operates/ColumnClassifyMgr'));
const ColumnClassifyDetail = lazy(() => import('@app/views/operates/ColumnClassifyDetail'));
const FilmTvMgr = lazy(() => import('@app/views/find/filmTvMgr'));
const FilmTvConMgr = lazy(() => import('@app/views/find/filmTvConMgr'));
const FilmTvHomePageMgr = lazy(() => import('@app/views/find/filmTvHomePageMgr'));
const GroupChatMgr = lazy(() => import('@app/views/operates/IM/groupChatMgr'));
const GroupChatMuteList = lazy(() => import('@app/views/operates/IM/groupChatMuteList'));
const messageReport = lazy(() => import('@app/views/operates/IM/messageReport'));
export default [
  {
    path: '/drawer',
    component: EditorChoiceness,
    routeProps: {
      breadCrumb: ['运营管理', '编辑精选'],
      selectKeys: ['/view/drawer'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/create',
    component: CreativeCenter,
    routeProps: {
      breadCrumb: ['运营管理', '创作中心'],
      selectKeys: ['/view/create'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/audioHomeMgr',
    component: AudioManager,
    routeProps: {
      breadCrumb: ['运营管理', '音频广场'],
      selectKeys: ['/view/audioHomeMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/im',
    component: ImManager,
    routeProps: {
      breadCrumb: ['运营管理', '私信管理'],
      selectKeys: ['/view/im'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/im-white-list',
    component: ImWhiteListManager,
    routeProps: {
      breadCrumb: ['运营管理', '私信管理', '私信白名单'],
      selectKeys: ['/view/im'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/im-black-list',
    component: ImBlackListManager,
    routeProps: {
      breadCrumb: ['运营管理', '私信管理', '私信黑名单'],
      selectKeys: ['/view/im'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/im-report-list',
    component: ImReportListManager,
    routeProps: {
      breadCrumb: ['运营管理', '私信管理', '私信举报'],
      selectKeys: ['/view/im'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/partyNewsMgr',
    component: PartyNewsMgr,
    routeProps: {
      breadCrumb: ['运营管理', '党政资讯合集'],
      selectKeys: ['/view/partyNewsMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/gptQuestion',
    component: AssistantManager,
    routeProps: {
      breadCrumb: ['运营管理', '智能助手'],
      selectKeys: ['/view/gptQuestion'],
      openKeys: ['/view/operatePages'],
      category: 0,
    },
    permission: '',
  },
  {
    path: '/OfficialDocMgr',
    component: AssistantManager,
    routeProps: {
      breadCrumb: ['运营管理', '公文搜索'],
      selectKeys: ['/view/OfficialDocMgr'],
      openKeys: ['/view/operatePages'],
      category: 3,
    },
    permission: '',
  },
  {
    path: '/CxkMgr',
    component: AssistantManager,
    routeProps: {
      breadCrumb: ['运营管理', '健康助手'],
      selectKeys: ['/view/CxkMgr'],
      openKeys: ['/view/operatePages'],
      category: 4,
    },
    permission: '',
  },
  {
    path: '/ranking',
    component: RankingManager,
    routeProps: {
      breadCrumb: ['运营管理', '榜单管理'],
      selectKeys: ['/view/ranking'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/redPacketActivityMgr',
    component: RedPacket,
    routeProps: {
      breadCrumb: ['运营管理', '红包活动管理'],
      selectKeys: ['/view/redPacketActivityMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/pushNotify',
    component: PushNotifyList,
    routeProps: {
      breadCrumb: ['运营管理', '推送管理'],
      selectKeys: ['/view/pushNotify'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/sensitiveWordsMgr',
    component: SensitiveWords,
    routeProps: {
      breadCrumb: ['运营管理', '敏感词管理'],
      selectKeys: ['/view/sensitiveWordsMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/startPage',
    component: StartPage,
    routeProps: {
      breadCrumb: ['运营管理', '启动页管理'],
      selectKeys: ['/view/startPage'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/startPageDevices/view/:id',
    component: StartPageDevices,
    routeProps: {
      breadCrumb: ['运营管理', '启动页管理', '查看设备图'],
      selectKeys: ['/view/startPage'],
      openKeys: ['/view/operatePages'],
      isEdit: false,
    },
    permission: '',
  },
  {
    path: '/startPageDevices/edit/:id',
    component: StartPageDevices,
    routeProps: {
      breadCrumb: ['运营管理', '启动页管理', '编辑设备图'],
      selectKeys: ['/view/startPage'],
      openKeys: ['/view/operatePages'],
      isEdit: true,
    },
    permission: '',
  },
  {
    path: '/aboutManager',
    component: AboutManager,
    routeProps: {
      breadCrumb: ['APP功能管理', '关于管理'],
      selectKeys: ['/view/aboutManager'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/privacyPolicyMgr',
    component: PrivacyPolicy,
    routeProps: {
      breadCrumb: ['APP功能管理', '隐私政策管理'],
      selectKeys: ['/view/privacyPolicyMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/shareWhiteHost',
    component: WhiteHost,
    routeProps: {
      breadCrumb: ['运营管理', '分享白名单管理'],
      selectKeys: ['/view/shareWhiteHost'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/appFeatureSwitch',
    component: AppFeatureSwitch,
    routeProps: {
      breadCrumb: ['运营管理', '特殊功能管理'],
      selectKeys: ['/view/appFeatureSwitch'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/activityRecommend',
    component: ProposalManager,
    routeProps: {
      breadCrumb: ['运营管理', '运营推荐位'],
      selectKeys: ['/view/activityRecommend'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/cfYtbMgr',
    component: ReadRankMgr,
    routeProps: {
      breadCrumb: ['运营管理', '春风悦读榜'],
      selectKeys: ['/view/cfYtbMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/ugcTopicMgr',
    component: TopicManager,
    routeProps: {
      breadCrumb: ['运营管理', '话题管理'],
      selectKeys: ['/view/ugcTopicMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/auditTopic',
    component: TopicAudit,
    routeProps: {
      breadCrumb: ['运营管理', '话题管理', '审核话题'],
      selectKeys: ['/view/ugcTopicMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/ugcTopicRecommend',
    component: UGCTopicRecommend,
    routeProps: {
      breadCrumb: ['运营管理', '话题管理', '话题推荐'],
      selectKeys: ['/view/ugcTopicRecommend'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/ugcTopicArticle/:id/:name/:type',
    component: TopicArticleList,
    routeProps: {
      breadCrumb: ['运营管理', '话题管理'],
      selectKeys: ['/view/ugcTopicMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/TopManuscript/:id/:type',
    component: TopManuscript,
    routeProps: {
      breadCrumb: ['运营管理', '话题管理', '置顶稿件管理'],
      selectKeys: ['/view/ugcTopicMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/topicClassMgr',
    component: TopicCategoryMgr,
    routeProps: {
      breadCrumb: ['运营管理', '话题分类管理'],
      selectKeys: ['/view/topicClassMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/dynamicSticker',
    component: StickerManager,
    routeProps: {
      breadCrumb: ['运营管理', '动态贴纸管理'],
      selectKeys: ['/view/dynamicSticker'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/redPacketMgr',
    component: RedPacket,
    routeProps: {
      breadCrumb: ['运营管理', '红包管理'],
      selectKeys: ['/view/redPacketMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/musicMgr',
    component: MusicManager,
    routeProps: {
      breadCrumb: ['运营管理', '音乐管理'],
      selectKeys: ['/view/musicMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/musicCategoryMgr',
    component: MusicCategoryManager,
    routeProps: {
      breadCrumb: ['运营管理', '音乐分类管理'],
      selectKeys: ['/view/musicMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/redpacketTotal',
    component: RedpacketTotal,
    routeProps: {
      breadCrumb: ['运营管理', '红包管理', '红包总额统计'],
      selectKeys: ['/view/redPacketMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/redpacketUserTotal',
    component: RedpacketUserTotal,
    routeProps: {
      breadCrumb: ['运营管理', '红包管理', '用户金额详情'],
      selectKeys: ['/view/redPacketMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/redpacketSummit/:data',
    component: RedpacketSingleTotal,
    routeProps: {
      breadCrumb: ['运营管理', '红包管理', '用户金额详情'],
      selectKeys: ['/view/redPacketMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/hotnews',
    component: HotNews,
    routeProps: {
      breadCrumb: ['运营管理', '热点新闻'],
      selectKeys: ['/view/hotnews'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/activityManager',
    component: ActivityManager,
    routeProps: {
      breadCrumb: ['运营管理', '活动管理'],
      selectKeys: ['/view/activityManager'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/adamantineMgr',
    component: AdamantineMgr,
    routeProps: {
      breadCrumb: ['运营管理', '活动管理', '金刚位管理'],
      selectKeys: ['/view/adamantineMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/hotwordManager',
    component: HotWord,
    routeProps: {
      breadCrumb: ['运营管理', '热搜词管理'],
      selectKeys: ['/view/hotwordManager'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/orgcontentrecommend',
    component: OrgContentRecommend,
    routeProps: {
      breadCrumb: ['运营管理', '潮鸣号内容推荐'],
      selectKeys: ['/view/orgcontentrecommend'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/achievementMedalManager',
    component: MedalManager,
    routeProps: {
      breadCrumb: ['运营管理', '成就勋章管理'],
      selectKeys: ['/view/achievementMedalManager'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/redpacketagreement',
    component: RedpacketAgreement,
    routeProps: {
      breadCrumb: ['运营管理', '红包管理', '红包协议'],
      selectKeys: ['/view/redPacketMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/signmanager',
    component: SignManager,
    routeProps: {
      breadCrumb: ['运营管理', '每日签到'],
      selectKeys: ['/view/signmanager'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/byteDynamicSticker',
    component: ByteStickerManager,
    routeProps: {
      breadCrumb: ['运营管理', '字节动效管理'],
      selectKeys: ['/view/byteDynamicSticker'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/arCnMgr',
    component: ArSceneManager,
    routeProps: {
      breadCrumb: ['运营管理', 'AR事件管理'],
      selectKeys: ['/view/arCnMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/movieCoupons',
    component: MovieCouponsManager,
    routeProps: {
      breadCrumb: ['运营管理', '电影消费券管理'],
      selectKeys: ['/view/movieCoupons'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/commentOperate',
    component: CommentOperate,
    routeProps: {
      breadCrumb: ['运营管理', '评论运营管理'],
      selectKeys: ['/view/commentOperate'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/columnManager',
    component: ColumnManager,
    routeProps: {
      breadCrumb: ['运营管理', '栏目管理'],
      selectKeys: ['/view/columnManager'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/columnClassifyManager',
    component: ColumnClassifyMgr,
    routeProps: {
      breadCrumb: ['运营管理', '栏目分类'],
      selectKeys: ['/view/columnManager'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/columnClassifyDetail/:id/:name',
    component: ColumnClassifyDetail,
    routeProps: {
      breadCrumb: ['运营管理', '栏目分类'],
      selectKeys: ['/view/columnManager'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/columnGroupMgr/:id/:name',
    component: ColumnGroupMgr,
    routeProps: {
      breadCrumb: ['运营管理', '栏目管理'],
      selectKeys: ['/view/columnManager'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/newsColumnArticleList/:id/:name',
    component: NewsColumnArticleList,
    routeProps: {
      breadCrumb: ['运营管理', '栏目管理'],
      selectKeys: ['/view/columnManager'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/appAdvert',
    component: AppAdvert,
    routeProps: {
      breadCrumb: ['运营管理', '广告管理'],
      selectKeys: ['/view/appAdvert'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/advertise',
    component: CommercialAd,
    routeProps: {
      breadCrumb: ['运营管理', '商业广告管理'],
      selectKeys: ['/view/advertise'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/advertiseCode',
    component: CommercialAdCode,
    routeProps: {
      breadCrumb: ['运营管理', '商业广告管理'],
      selectKeys: ['/view/advertiseCode'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/longVideoDurationMgr',
    component: longVideo,
    routeProps: {
      breadCrumb: ['运营管理', '长视频创作管理'],
      selectKeys: ['/view/longVideoDurationMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/longVideoUser/:id',
    component: longVideoUser,
    routeProps: {
      breadCrumb: ['运营管理', '长视频创作管理', '长视频用户管理'],
      selectKeys: ['/view/longVideoUser'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/tagMgr',
    component: TagManager,
    routeProps: {
      breadCrumb: ['运营管理', '标签管理'],
      selectKeys: ['/view/tagMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/tagContentList/:id/:name',
    component: TagContentList,
    routeProps: {
      breadCrumb: ['运营管理', '标签管理'],
      selectKeys: ['/view/tagContentList'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/appThemeMgr',
    component: AppThemeMgr,
    routeProps: {
      breadCrumb: ['运营管理', '主题管理'],
      selectKeys: ['/view/appThemeMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  ///主题首页提醒
  {
    path: '/homeRemind',
    component: HomeRemind,
    routeProps: {
      breadCrumb: ['运营管理', '新主题首页提醒'],
      selectKeys: ['/view/homeRemind'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/hdzxMgr',
    component: HdzxMgr,
    routeProps: {
      breadCrumb: ['运营管理', '头像挂件'],
      selectKeys: ['/view/hdzxMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/aiFeedback',
    component: AiFeedback,
    routeProps: {
      breadCrumb: ['运营管理', '智能创作反馈'],
      selectKeys: ['/view/aiFeedback'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/media_content',
    component: FilmTvMgr,
    routeProps: {
      breadCrumb: ['运营管理', ' 影视内容管理'],
      selectKeys: ['/view/media_content'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/media_content_list',
    component: FilmTvConMgr,
    routeProps: {
      breadCrumb: ['运营管理', ' 影视内容管理'],
      selectKeys: ['/view/media_content'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/media_home',
    component: FilmTvHomePageMgr,
    routeProps: {
      breadCrumb: ['运营管理', '影视首页管理'],
      selectKeys: ['/view/media_home'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/im_group',
    component: GroupChatMgr,
    routeProps: {
      breadCrumb: ['运营管理', 'IM消息管理', '群聊管理'],
      selectKeys: ['/view/im_group'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/im_forbid',
    component: GroupChatMuteList,
    routeProps: {
      breadCrumb: ['运营管理', 'IM消息管理', '群聊禁言'],
      selectKeys: ['/view/im_forbid'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/im_complain',
    component: messageReport,
    routeProps: {
      breadCrumb: ['运营管理', 'IM消息管理', '群聊举报'],
      selectKeys: ['/view/im_complain'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
];
